<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Location Tree View -->
        <record id="view_location_tree" model="ir.ui.view">
            <field name="name">extended.attendance.location.tree</field>
            <field name="model">extended.attendance.location</field>
            <field name="arch" type="xml">
                <tree string="Attendance Locations" decoration-muted="not active">
                    <field name="sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="location_path"/>
                    <field name="current_occupancy"/>
                    <field name="capacity"/>
                    <field name="requires_permission"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- Location Form View -->
        <record id="view_location_form" model="ir.ui.view">
            <field name="name">extended.attendance.location.form</field>
            <field name="model">extended.attendance.location</field>
            <field name="arch" type="xml">
                <form string="Attendance Location">
                    <header>
                        <field name="active" widget="boolean_toggle"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_attendance" type="object" class="oe_stat_button" icon="fa-clock-o">
                                <field name="attendance_count" widget="statinfo" string="Attendances"/>
                            </button>
                            <button name="action_view_current_occupancy" type="object" class="oe_stat_button" icon="fa-users">
                                <field name="current_occupancy" widget="statinfo" string="Current"/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Location Name"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="code"/>
                                <field name="sequence"/>
                                <field name="color" widget="color"/>
                                <field name="parent_location_id"/>
                            </group>
                            <group>
                                <field name="building"/>
                                <field name="floor"/>
                                <field name="room_number"/>
                                <field name="capacity"/>
                            </group>
                        </group>
                        
                        <group string="Access Control">
                            <group>
                                <field name="requires_permission"/>
                                <field name="allowed_person_type_ids" widget="many2many_tags" attrs="{'invisible': [('requires_permission', '=', False)]}"/>
                            </group>
                        </group>
                        
                        <group string="Geographic Location">
                            <group>
                                <field name="latitude"/>
                                <field name="longitude"/>
                            </group>
                        </group>
                        
                        <field name="description" placeholder="Description of this location..."/>
                        
                        <notebook>
                            <page string="Operating Hours">
                                <group>
                                    <field name="has_operating_hours"/>
                                </group>
                                <field name="operating_hours_ids" attrs="{'invisible': [('has_operating_hours', '=', False)]}">
                                    <tree editable="bottom">
                                        <field name="weekday"/>
                                        <field name="time_from" widget="float_time"/>
                                        <field name="time_to" widget="float_time"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Devices">
                                <field name="device_ids">
                                    <tree editable="bottom">
                                        <field name="name"/>
                                        <field name="device_id"/>
                                        <field name="device_type"/>
                                        <field name="active"/>
                                        <field name="last_sync"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Child Locations">
                                <field name="child_location_ids">
                                    <tree>
                                        <field name="name"/>
                                        <field name="code"/>
                                        <field name="current_occupancy"/>
                                        <field name="capacity"/>
                                        <field name="active"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Location Search View -->
        <record id="view_location_search" model="ir.ui.view">
            <field name="name">extended.attendance.location.search</field>
            <field name="model">extended.attendance.location</field>
            <field name="arch" type="xml">
                <search string="Attendance Locations">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="building"/>
                    <field name="floor"/>
                    <field name="description"/>
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                    <filter string="Requires Permission" name="requires_permission" domain="[('requires_permission', '=', True)]"/>
                    <filter string="Has Operating Hours" name="has_hours" domain="[('has_operating_hours', '=', True)]"/>
                    <filter string="Currently Occupied" name="occupied" domain="[('current_occupancy', '>', 0)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Building" name="group_building" context="{'group_by': 'building'}"/>
                        <filter string="Floor" name="group_floor" context="{'group_by': 'floor'}"/>
                        <filter string="Parent Location" name="group_parent" context="{'group_by': 'parent_location_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Location Action -->
        <record id="action_location" model="ir.actions.act_window">
            <field name="name">Attendance Locations</field>
            <field name="res_model">extended.attendance.location</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first attendance location!
                </p>
                <p>
                    Attendance locations define where people can check in and out.
                    You can create locations for different areas like entrances, offices, halls, etc.
                </p>
            </field>
        </record>

    </data>
</odoo>
